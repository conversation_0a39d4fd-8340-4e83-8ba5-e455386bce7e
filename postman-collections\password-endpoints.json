{"info": {"_postman_id": "password-endpoints-collection", "name": "Password Management & Project Search Endpoints", "description": "Collection for testing password change, reset endpoints, and project search/filtering", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "variable": [{"key": "base_url", "value": "http://localhost:8080", "type": "string"}, {"key": "auth_token", "value": "", "type": "string"}], "item": [{"name": "Change Password (Me)", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response has success message', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('message');", "    pm.expect(responseJson.message).to.eql('Password changed successfully');", "});"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{auth_token}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"current_password\": \"password123\",\n  \"new_password\": \"newpassword123\"\n}"}, "url": {"raw": "{{base_url}}/me/change-password", "host": ["{{base_url}}"], "path": ["me", "change-password"]}, "description": "Change the current user's password. Requires authentication and current password verification."}, "response": [{"name": "Success Response", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"current_password\": \"password123\",\n  \"new_password\": \"newpassword123\"\n}"}, "url": {"raw": "{{base_url}}/me/change-password", "host": ["{{base_url}}"], "path": ["me", "change-password"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n  \"message\": \"Password changed successfully\"\n}"}, {"name": "Invalid Current Password", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"current_password\": \"wrongpassword\",\n  \"new_password\": \"newpassword123\"\n}"}, "url": {"raw": "{{base_url}}/me/change-password", "host": ["{{base_url}}"], "path": ["me", "change-password"]}}, "status": "Bad Request", "code": 400, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n  \"code\": \"INVALID_CURRENT_PASSWORD\",\n  \"message\": \"Current password is incorrect\"\n}"}]}, {"name": "Reset User Password (Admin)", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response contains user data', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('id');", "    pm.expect(responseJson).to.have.property('email');", "    pm.expect(responseJson).to.have.property('is_required_reset_password');", "    pm.expect(responseJson.is_required_reset_password).to.be.false;", "});"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{auth_token}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"new_password\": \"resetpassword123\"\n}"}, "url": {"raw": "{{base_url}}/users/:user_id/reset-password", "host": ["{{base_url}}"], "path": ["users", ":user_id", "reset-password"], "variable": [{"key": "user_id", "value": "user-uuid-here", "description": "The ID of the user whose password should be reset"}]}, "description": "Reset a user's password (admin function). Requires authentication and admin privileges."}, "response": [{"name": "Success Response", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"new_password\": \"resetpassword123\"\n}"}, "url": {"raw": "{{base_url}}/users/user-uuid-here/reset-password", "host": ["{{base_url}}"], "path": ["users", "user-uuid-here", "reset-password"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n  \"id\": \"user-uuid-here\",\n  \"email\": \"<EMAIL>\",\n  \"display_name\": \"User Name\",\n  \"type\": \"USER\",\n  \"is_required_reset_password\": false,\n  \"created_at\": \"2024-01-01T00:00:00Z\",\n  \"updated_at\": \"2024-01-01T00:00:00Z\"\n}"}, {"name": "User Not Found", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"new_password\": \"resetpassword123\"\n}"}, "url": {"raw": "{{base_url}}/users/invalid-uuid/reset-password", "host": ["{{base_url}}"], "path": ["users", "invalid-uuid", "reset-password"]}}, "status": "Not Found", "code": 404, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n  \"code\": \"USER_NOT_FOUND\",\n  \"message\": \"User not found\"\n}"}]}, {"name": "Project Search and Filtering", "item": [{"name": "Search Projects", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response has pagination structure', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson).to.have.property('meta');", "    pm.expect(responseJson.meta).to.have.property('total');", "});"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{auth_token}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{base_url}}/projects?search=project name", "host": ["{{base_url}}"], "path": ["projects"], "query": [{"key": "search", "value": "project name", "description": "Search in project name, contact name, and contact email"}]}, "description": "Search projects by name, contact name, or contact email"}}, {"name": "Filter Projects by Ministry", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{auth_token}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{base_url}}/projects?ministry_id=ministry-uuid", "host": ["{{base_url}}"], "path": ["projects"], "query": [{"key": "ministry_id", "value": "ministry-uuid", "description": "Filter by ministry ID"}]}, "description": "Filter projects by ministry"}}, {"name": "Combined Filters", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{auth_token}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{base_url}}/projects?search=project&ministry_id=ministry-uuid&department_id=dept-uuid", "host": ["{{base_url}}"], "path": ["projects"], "query": [{"key": "search", "value": "project", "description": "Search term"}, {"key": "ministry_id", "value": "ministry-uuid", "description": "Filter by ministry"}, {"key": "department_id", "value": "dept-uuid", "description": "Filter by department"}]}, "description": "Combine search with multiple filters"}}]}]}