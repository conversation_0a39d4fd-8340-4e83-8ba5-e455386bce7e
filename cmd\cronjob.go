package cmd

import (
	"fmt"
	"net/http"
	"os"
	"time"

	"gitlab.finema.co/finema/csp/csp-api/modules/projectusage"
	core "gitlab.finema.co/finema/idin-core"
)

func CronjobRun() {
	env := core.NewEnv()

	db, err := core.NewDatabase(env.Config()).Connect()
	if err != nil {
		fmt.Fprintf(os.Stderr, "db: %v", err)
		os.Exit(1)
	}

	// Start the health check endpoint and make sure not to block
	go func() {
		_ = http.ListenAndServe(env.Config().Host, http.HandlerFunc(
			func(w http.ResponseWriter, r *http.Request) {
				_, _ = w.Write([]byte("ok"))
			},
		))
	}()

	e := core.NewCronjobContext(&core.CronjobContextOptions{
		TimeLocation: time.UTC,
		ContextOptions: &core.ContextOptions{
			DB:  db,
			ENV: env,
		},
	})

	projectusage.NewProjectUsageCronjob(e)

	e.Start()
}
