package auth

import (
	"net/http"

	"gitlab.finema.co/finema/csp/csp-api/requests"
	"gitlab.finema.co/finema/csp/csp-api/services"
	core "gitlab.finema.co/finema/idin-core"
)

type AuthController struct {
}

func (m AuthController) Logout(c core.IHTTPContext) error {
	authSvc := services.NewAuthService(c)
	err := authSvc.Logout(c.GetUser().Data["token"])
	if err != nil {
		return c.JSON(err.GetStatus(), err.JSON())
	}

	return c.NoContent(http.StatusNoContent)
}

func (m AuthController) Login(c core.IHTTPContext) error {
	input := &requests.Login{}
	if err := c.Bind<PERSON>ith<PERSON>alidate(input); err != nil {
		return c.JSON(err.GetStatus(), err.<PERSON><PERSON><PERSON>())
	}

	authSvc := services.NewAuthService(c)
	payload := &services.LoginPayload{
		Email:      *input.Email,
		Password:   *input.Password,
		IPAddress:  c.RealIP(),
		UserAgent:  c.Request().UserAgent(),
		DeviceInfo: c.Request().UserAgent(), // Using UserAgent as DeviceInfo for now
	}

	loginResponse, err := authSvc.Login(payload)
	if err != nil {
		return c.JSON(err.GetStatus(), err.JSON())
	}

	return c.JSON(http.StatusOK, loginResponse)
}
