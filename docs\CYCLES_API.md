# Cycles API Documentation

## Overview
The Cycles API provides endpoints for managing billing cycles in the CSP (Cloud Service Provider) system. It supports pagination, search functionality, and filtering by date ranges.

## Authentication
All endpoints require authentication using Bear<PERSON> token in the Authorization header:
```
Authorization: Bearer <your_access_token>
```

## Endpoints

### 1. Get All Cycles (Pagination)
**GET** `/cycles`

Retrieves a paginated list of all cycles.

#### Query Parameters
- `page` (optional): Page number (default: 1)
- `limit` (optional): Items per page (default: 10)
- `q` (optional): Search query (searches in ID, start date, and end date)
- `order_by` (optional): Order by field (default: "cycle_start_date DESC")

#### Example Request
```bash
GET /cycles?page=1&limit=10&q=2024
```

#### Example Response
```json
{
  "data": [
    {
      "id": "550e8400-e29b-41d4-a716-************",
      "cycle_start_date": "2024-01-01T00:00:00Z",
      "cycle_end_date": "2024-01-31T23:59:59Z",
      "status": "completed",
      "hour_count": 744,
      "total_cost": 15000.50,
      "total_official_cost": 14500.00,
      "created_at": "2024-01-01T00:00:00Z",
      "updated_at": "2024-02-01T00:00:00Z",
      "project_usages": [...]
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 10,
    "total": 50,
    "total_pages": 5
  }
}
```

### 2. Get Cycles by Date Range
**GET** `/cycles?start_date=YYYY-MM-DD&end_date=YYYY-MM-DD`

Retrieves cycles filtered by date range with pagination.

#### Query Parameters
- `start_date` (optional): Filter cycles starting from this date (YYYY-MM-DD format)
- `end_date` (optional): Filter cycles ending before this date (YYYY-MM-DD format)
- `page` (optional): Page number (default: 1)
- `limit` (optional): Items per page (default: 10)
- `q` (optional): Search query

#### Example Request
```bash
GET /cycles?start_date=2024-01-01&end_date=2024-12-31&page=1&limit=5
```

### 3. Get Current Cycle
**GET** `/cycles/current`

Retrieves the currently active cycle (status = "current").

#### Example Request
```bash
GET /cycles/current
```

#### Example Response
```json
{
  "id": "550e8400-e29b-41d4-a716-************",
  "cycle_start_date": "2024-08-01T00:00:00Z",
  "cycle_end_date": "2024-08-31T23:59:59Z",
  "status": "current",
  "hour_count": 744,
  "total_cost": 12000.00,
  "total_official_cost": 11500.00,
  "created_at": "2024-08-01T00:00:00Z",
  "updated_at": "2024-08-14T10:30:00Z",
  "project_usages": [...]
}
```

### 4. Get Cycle by ID
**GET** `/cycles/{id}`

Retrieves a specific cycle by its ID.

#### Path Parameters
- `id`: The UUID of the cycle

#### Example Request
```bash
GET /cycles/550e8400-e29b-41d4-a716-************
```

#### Example Response
```json
{
  "id": "550e8400-e29b-41d4-a716-************",
  "cycle_start_date": "2024-01-01T00:00:00Z",
  "cycle_end_date": "2024-01-31T23:59:59Z",
  "status": "completed",
  "hour_count": 744,
  "total_cost": 15000.50,
  "total_official_cost": 14500.00,
  "created_at": "2024-01-01T00:00:00Z",
  "updated_at": "2024-02-01T00:00:00Z",
  "project_usages": [
    {
      "id": "project_usage_id",
      "project_id": "project_id",
      "cycle_id": "550e8400-e29b-41d4-a716-************",
      "usage_data": {...}
    }
  ]
}
```

## Data Model

### Cycle Object
- `id`: UUID - Unique identifier for the cycle
- `cycle_start_date`: DateTime - Start date of the billing cycle
- `cycle_end_date`: DateTime - End date of the billing cycle
- `status`: String - Status of the cycle (e.g., "current", "completed", "pending")
- `hour_count`: Integer - Total hours in the cycle
- `total_cost`: Float - Total cost for the cycle
- `total_official_cost`: Float - Official total cost for the cycle
- `created_at`: DateTime - When the cycle was created
- `updated_at`: DateTime - When the cycle was last updated
- `project_usages`: Array - Related project usage records (included when available)

## Error Responses

### 404 Not Found
```json
{
  "status": 404,
  "code": "NOT_FOUND",
  "message": "Cycle not found"
}
```

### 401 Unauthorized
```json
{
  "status": 401,
  "code": "UNAUTHORIZED",
  "message": "Invalid or missing authentication token"
}
```

### 500 Internal Server Error
```json
{
  "status": 500,
  "code": "INTERNAL_ERROR",
  "message": "Internal server error"
}
```

## Usage Examples

### Using cURL

1. **Get all cycles with pagination:**
```bash
curl -X GET "http://localhost:8080/cycles?page=1&limit=10" \
  -H "Authorization: Bearer your_access_token"
```

2. **Search cycles:**
```bash
curl -X GET "http://localhost:8080/cycles?q=2024&page=1&limit=5" \
  -H "Authorization: Bearer your_access_token"
```

3. **Get current cycle:**
```bash
curl -X GET "http://localhost:8080/cycles/current" \
  -H "Authorization: Bearer your_access_token"
```

4. **Get specific cycle:**
```bash
curl -X GET "http://localhost:8080/cycles/550e8400-e29b-41d4-a716-************" \
  -H "Authorization: Bearer your_access_token"
```

## Postman Collection
Import the provided Postman collection (`postman/Cycles_API.postman_collection.json`) to test all endpoints easily. Make sure to set the following variables:
- `base_url`: Your API base URL (e.g., http://localhost:8080)
- `access_token`: Your authentication token
- `cycle_id`: A valid cycle ID for testing the get by ID endpoint
