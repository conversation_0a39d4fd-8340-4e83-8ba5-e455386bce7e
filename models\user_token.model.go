package models

type UserToken struct {
	BaseModelHardDelete
	UserID     string `json:"user_id" gorm:"column:user_id"`
	Token      string `json:"token" gorm:"column:token"`
	IPAddress  string `json:"ip_address" gorm:"column:ip_address"`
	UserAgent  string `json:"user_agent" gorm:"column:user_agent"`
	DeviceInfo string `json:"device_info" gorm:"column:device_info"`
}

func (UserToken) TableName() string {
	return "user_tokens"
}
