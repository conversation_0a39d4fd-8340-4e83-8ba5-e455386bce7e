package division

import (
	"net/http"

	"gitlab.finema.co/finema/csp/csp-api/requests"
	"gitlab.finema.co/finema/csp/csp-api/services"
	core "gitlab.finema.co/finema/idin-core"
	"gitlab.finema.co/finema/idin-core/utils"
)

type DivisionController struct {
}

func (m DivisionController) Pagination(c core.IHTTPContext) error {
	divisionSvc := services.NewDivisionService(c)
	
	// Check if filtering by ministry or department
	ministryID := c.QueryParam("ministry_id")
	departmentID := c.QueryParam("department_id")
	
	if ministryID != "" {
		res, ierr := divisionSvc.FindByMinistry(ministryID, c.GetPageOptions())
		if ierr != nil {
			return c.JSON(ierr.GetStatus(), ierr.JSON())
		}
		return c.JSON(http.StatusOK, res)
	}
	
	if departmentID != "" {
		res, ierr := divisionSvc.FindByDepartment(departmentID, c.GetPageOptions())
		if ierr != nil {
			return c.JSON(ierr.GetStatus(), ierr.JSON())
		}
		return c.JSON(http.StatusOK, res)
	}

	res, ierr := divisionSvc.Pagination(c.GetPageOptions())
	if ierr != nil {
		return c.JSON(ierr.GetStatus(), ierr.JSON())
	}

	return c.JSON(http.StatusOK, res)
}

func (m DivisionController) Find(c core.IHTTPContext) error {
	divisionSvc := services.NewDivisionService(c)
	division, err := divisionSvc.Find(c.Param("id"))
	if err != nil {
		return c.JSON(err.GetStatus(), err.JSON())
	}

	return c.JSON(http.StatusOK, division)
}

func (m DivisionController) Create(c core.IHTTPContext) error {
	input := &requests.DivisionCreate{}
	if err := c.BindWithValidate(input); err != nil {
		return c.JSON(err.GetStatus(), err.JSON())
	}

	divisionSvc := services.NewDivisionService(c)
	payload := &services.DivisionCreatePayload{}
	_ = utils.Copy(payload, input)
	division, err := divisionSvc.Create(payload)
	if err != nil {
		return c.JSON(err.GetStatus(), err.JSON())
	}

	return c.JSON(http.StatusCreated, division)
}

func (m DivisionController) Update(c core.IHTTPContext) error {
	input := &requests.DivisionUpdate{}
	if err := c.BindWithValidate(input); err != nil {
		return c.JSON(err.GetStatus(), err.JSON())
	}

	divisionSvc := services.NewDivisionService(c)
	payload := &services.DivisionUpdatePayload{}
	_ = utils.Copy(payload, input)
	division, err := divisionSvc.Update(c.Param("id"), payload)
	if err != nil {
		return c.JSON(err.GetStatus(), err.JSON())
	}

	return c.JSON(http.StatusOK, division)
}

func (m DivisionController) Delete(c core.IHTTPContext) error {
	divisionSvc := services.NewDivisionService(c)
	err := divisionSvc.Delete(c.Param("id"))
	if err != nil {
		return c.JSON(err.GetStatus(), err.JSON())
	}

	return c.NoContent(http.StatusNoContent)
}
