package cycle

import (
	"net/http"

	"gitlab.finema.co/finema/csp/csp-api/services"
	core "gitlab.finema.co/finema/idin-core"
)

type CycleController struct {
}

func (m CycleController) Pagination(c core.IHTTPContext) error {
	cycleSvc := services.NewCycleService(c)
	
	// Check if filtering by date range
	startDate := c.Query<PERSON>aram("start_date")
	endDate := c.QueryParam("end_date")
	
	if startDate != "" || endDate != "" {
		res, ierr := cycleSvc.FindByDateRange(startDate, endDate, c.GetPageOptions())
		if ierr != nil {
			return c.JSON(ierr.GetStatus(), ierr.JSON())
		}
		return c.JSON(http.StatusOK, res)
	}

	res, ierr := cycleSvc.Pagination(c.GetPageOptions())
	if ierr != nil {
		return c.JSON(ierr.GetStatus(), ierr.JSON())
	}

	return c.<PERSON><PERSON><PERSON>(http.StatusOK, res)
}

func (m CycleController) Find(c core.IHTTPContext) error {
	cycleSvc := services.NewCycleService(c)
	cycle, err := cycleSvc.Find(c.Param("id"))
	if err != nil {
		return c.JSON(err.GetStatus(), err.JSON())
	}

	return c.JSON(http.StatusOK, cycle)
}

func (m CycleController) FindCurrent(c core.IHTTPContext) error {
	cycleSvc := services.NewCycleService(c)
	cycle, err := cycleSvc.FindCurrent()
	if err != nil {
		return c.JSON(err.GetStatus(), err.JSON())
	}

	return c.JSON(http.StatusOK, cycle)
}
