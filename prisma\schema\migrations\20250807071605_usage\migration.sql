/*
  Warnings:

  - Added the required column `official_amount` to the `project_usages` table without a default value. This is not possible if the table is not empty.

*/
-- AlterTable
ALTER TABLE "public"."cycles" ADD COLUMN     "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
ADD COLUMN     "hour_count" INTEGER,
ADD COLUMN     "status" TEXT,
ADD COLUMN     "total_cost" DOUBLE PRECISION,
ADD COLUMN     "total_official_cost" DOUBLE PRECISION,
ADD COLUMN     "updated_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP;

-- AlterTable
ALTER TABLE "public"."project_usages" ADD COLUMN     "official_amount" DOUBLE PRECISION NOT NULL,
ADD COLUMN     "timestamp" TIMESTAMP(3),
ADD COLUMN     "updated_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP;

-- AlterTable
ALTER TABLE "public"."projects" ADD COLUMN     "account_id" TEXT NOT NULL DEFAULT '';
