{"info": {"_postman_id": "cycles-api-collection", "name": "Cycles API", "description": "API endpoints for managing billing cycles", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "Get All Cycles (Pagination)", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}", "type": "text"}], "url": {"raw": "{{base_url}}/cycles?page=1&limit=10&q=", "host": ["{{base_url}}"], "path": ["cycles"], "query": [{"key": "page", "value": "1", "description": "Page number (default: 1)"}, {"key": "limit", "value": "10", "description": "Items per page (default: 10)"}, {"key": "q", "value": "", "description": "Search query (optional)"}, {"key": "order_by", "value": "cycle_start_date DESC", "description": "Order by field (optional)", "disabled": true}]}}, "response": []}, {"name": "Get Cycles by Date Range", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}", "type": "text"}], "url": {"raw": "{{base_url}}/cycles?start_date=2024-01-01&end_date=2024-12-31&page=1&limit=10", "host": ["{{base_url}}"], "path": ["cycles"], "query": [{"key": "start_date", "value": "2024-01-01", "description": "Filter cycles starting from this date (YYYY-MM-DD)"}, {"key": "end_date", "value": "2024-12-31", "description": "Filter cycles ending before this date (YYYY-MM-DD)"}, {"key": "page", "value": "1"}, {"key": "limit", "value": "10"}]}}, "response": []}, {"name": "Get Current Cycle", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}", "type": "text"}], "url": {"raw": "{{base_url}}/cycles/current", "host": ["{{base_url}}"], "path": ["cycles", "current"]}}, "response": []}, {"name": "Get Cycle by ID", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}", "type": "text"}], "url": {"raw": "{{base_url}}/cycles/{{cycle_id}}", "host": ["{{base_url}}"], "path": ["cycles", "{{cycle_id}}"]}}, "response": []}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "variable": [{"key": "base_url", "value": "http://localhost:8080", "type": "string"}, {"key": "access_token", "value": "", "type": "string"}, {"key": "cycle_id", "value": "", "type": "string"}]}