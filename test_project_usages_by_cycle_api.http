### Project Usages by Cycle API Test File
### This file demonstrates the new projects/:id/usages/cycles/:cycle_id endpoint

@base_url = http://localhost:3001
@auth_token = your_auth_token_here
@project_id = your_project_id_here
@cycle_id = your_cycle_id_here

### Get Project Usages by Specific Cycle (Default pagination)
GET {{base_url}}/projects/{{project_id}}/usages/cycles/{{cycle_id}}
Authorization: Bearer {{auth_token}}

### Get Project Usages by Specific Cycle with Pagination
GET {{base_url}}/projects/{{project_id}}/usages/cycles/{{cycle_id}}?page=1&limit=10
Authorization: Bearer {{auth_token}}

### Get Project Usages by Specific Cycle with Search
GET {{base_url}}/projects/{{project_id}}/usages/cycles/{{cycle_id}}?q=search_term&page=1&limit=5
Authorization: Bearer {{auth_token}}

### Get Project Usages by Specific Cycle with Custom Ordering
GET {{base_url}}/projects/{{project_id}}/usages/cycles/{{cycle_id}}?order_by=amount DESC&page=1&limit=10
Authorization: Bearer {{auth_token}}

### Compare with existing endpoints

### Get Project Usages (Current Cycle Only - Default)
GET {{base_url}}/projects/{{project_id}}/usages
Authorization: Bearer {{auth_token}}

### Get Project Usages (All Cycles)
GET {{base_url}}/projects/{{project_id}}/usages?all=true
Authorization: Bearer {{auth_token}}

### Get Current Cycle (to get cycle_id for testing)
GET {{base_url}}/cycles/current
Authorization: Bearer {{auth_token}}

### Get All Cycles (to get other cycle_ids for testing)
GET {{base_url}}/cycles?page=1&limit=10
Authorization: Bearer {{auth_token}}
