{"info": {"_postman_id": "csp-api-collection", "name": "CSP API Collection", "description": "Complete API collection for CSP (Civil Service Portal) API organized by modules", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{auth_token}}", "type": "string"}]}, "variable": [{"key": "base_url", "value": "http://localhost:3001", "type": "string"}, {"key": "auth_token", "value": "", "type": "string"}, {"key": "ministry_id", "value": "", "type": "string"}, {"key": "department_id", "value": "", "type": "string"}, {"key": "division_id", "value": "", "type": "string"}, {"key": "user_id", "value": "", "type": "string"}], "item": [{"name": "Home", "item": [{"name": "Health Check", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/", "host": ["{{base_url}}"], "path": [""]}}, "response": []}]}, {"name": "Authentication", "item": [{"name": "<PERSON><PERSON>", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const response = pm.response.json();", "    pm.collectionVariables.set('auth_token', response.token);", "}"], "type": "text/javascript"}}], "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"password\": \"password123\"\n}"}, "url": {"raw": "{{base_url}}/auth/login", "host": ["{{base_url}}"], "path": ["auth", "login"]}}, "response": []}, {"name": "Logout", "request": {"method": "POST", "header": [], "url": {"raw": "{{base_url}}/auth/logout", "host": ["{{base_url}}"], "path": ["auth", "logout"]}}, "response": []}]}, {"name": "User Profile (Me)", "item": [{"name": "Get My Profile", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/me", "host": ["{{base_url}}"], "path": ["me"]}}, "response": []}, {"name": "Update My Profile", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"display_name\": \"Updated Display Name\"\n}"}, "url": {"raw": "{{base_url}}/me", "host": ["{{base_url}}"], "path": ["me"]}}, "response": []}, {"name": "Get My Devices", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/me/devices", "host": ["{{base_url}}"], "path": ["me", "devices"]}}, "response": []}, {"name": "Delete My Device", "request": {"method": "DELETE", "header": [], "url": {"raw": "{{base_url}}/me/devices/:id", "host": ["{{base_url}}"], "path": ["me", "devices", ":id"], "variable": [{"key": "id", "value": "device-id-here"}]}}, "response": []}]}, {"name": "User Management", "item": [{"name": "Get Users (Pagination)", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "GET", "header": [], "url": {"raw": "{{base_url}}/users?page=1&limit=10", "host": ["{{base_url}}"], "path": ["users"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "10"}]}}, "response": []}, {"name": "Get User by ID", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "GET", "header": [], "url": {"raw": "{{base_url}}/users/:id", "host": ["{{base_url}}"], "path": ["users", ":id"], "variable": [{"key": "id", "value": "user-id-here"}]}}, "response": []}, {"name": "Create User", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"password\": \"password123\",\n  \"display_name\": \"New User\"\n}"}, "url": {"raw": "{{base_url}}/users", "host": ["{{base_url}}"], "path": ["users"]}}, "response": []}, {"name": "Update User", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"display_name\": \"Updated User Name\"\n}"}, "url": {"raw": "{{base_url}}/users/:id", "host": ["{{base_url}}"], "path": ["users", ":id"], "variable": [{"key": "id", "value": "user-id-here"}]}}, "response": []}, {"name": "Delete User", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "DELETE", "header": [], "url": {"raw": "{{base_url}}/users/:id", "host": ["{{base_url}}"], "path": ["users", ":id"], "variable": [{"key": "id", "value": "user-id-here"}]}}, "response": []}]}, {"name": "Ministry Management", "item": [{"name": "Get Ministries (Pagination)", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/ministries?page=1&limit=10", "host": ["{{base_url}}"], "path": ["ministries"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "10"}]}}, "response": []}, {"name": "Get Ministry by ID", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/ministries/:id", "host": ["{{base_url}}"], "path": ["ministries", ":id"], "variable": [{"key": "id", "value": "ministry-id-here"}]}}, "response": []}, {"name": "Create Ministry", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name_th\": \"กระทรวงการคลัง\",\n  \"name_en\": \"Ministry of Finance\",\n  \"code\": 1001,\n  \"short_name_th\": \"กค.\",\n  \"short_name_en\": \"MOF\"\n}"}, "url": {"raw": "{{base_url}}/ministries", "host": ["{{base_url}}"], "path": ["ministries"]}}, "response": []}, {"name": "Update Ministry", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name_th\": \"กระทรวงการคลัง (อัปเดต)\",\n  \"name_en\": \"Ministry of Finance (Updated)\",\n  \"code\": 1001,\n  \"short_name_th\": \"กค.\",\n  \"short_name_en\": \"MOF\"\n}"}, "url": {"raw": "{{base_url}}/ministries/:id", "host": ["{{base_url}}"], "path": ["ministries", ":id"], "variable": [{"key": "id", "value": "ministry-id-here"}]}}, "response": []}, {"name": "Delete Ministry", "request": {"method": "DELETE", "header": [], "url": {"raw": "{{base_url}}/ministries/:id", "host": ["{{base_url}}"], "path": ["ministries", ":id"], "variable": [{"key": "id", "value": "ministry-id-here"}]}}, "response": []}]}, {"name": "Department Management", "item": [{"name": "Get Departments (Pagination)", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/departments?page=1&limit=10", "host": ["{{base_url}}"], "path": ["departments"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "10"}]}}, "response": []}, {"name": "Get Departments by Ministry", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/departments?ministry_id={{ministry_id}}&page=1&limit=10", "host": ["{{base_url}}"], "path": ["departments"], "query": [{"key": "ministry_id", "value": "{{ministry_id}}"}, {"key": "page", "value": "1"}, {"key": "limit", "value": "10"}]}}, "response": []}, {"name": "Get Department by ID", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/departments/:id", "host": ["{{base_url}}"], "path": ["departments", ":id"], "variable": [{"key": "id", "value": "department-id-here"}]}}, "response": []}, {"name": "Create Department", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"ministry_id\": \"{{ministry_id}}\",\n  \"name_th\": \"กรมบัญชีกลาง\",\n  \"name_en\": \"Comptroller General's Department\",\n  \"code\": 2001,\n  \"short_name_th\": \"กบก.\",\n  \"short_name_en\": \"CGD\"\n}"}, "url": {"raw": "{{base_url}}/departments", "host": ["{{base_url}}"], "path": ["departments"]}}, "response": []}, {"name": "Update Department", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"ministry_id\": \"{{ministry_id}}\",\n  \"name_th\": \"กรมบัญชีกลาง (อัปเดต)\",\n  \"name_en\": \"Comptroller General's Department (Updated)\",\n  \"code\": 2001,\n  \"short_name_th\": \"กบก.\",\n  \"short_name_en\": \"CGD\"\n}"}, "url": {"raw": "{{base_url}}/departments/:id", "host": ["{{base_url}}"], "path": ["departments", ":id"], "variable": [{"key": "id", "value": "department-id-here"}]}}, "response": []}, {"name": "Delete Department", "request": {"method": "DELETE", "header": [], "url": {"raw": "{{base_url}}/departments/:id", "host": ["{{base_url}}"], "path": ["departments", ":id"], "variable": [{"key": "id", "value": "department-id-here"}]}}, "response": []}]}, {"name": "Division Management", "item": [{"name": "Get Divisions (Pagination)", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/divisions?page=1&limit=10", "host": ["{{base_url}}"], "path": ["divisions"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "10"}]}}, "response": []}, {"name": "Get Divisions by Department", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/divisions?department_id={{department_id}}&page=1&limit=10", "host": ["{{base_url}}"], "path": ["divisions"], "query": [{"key": "department_id", "value": "{{department_id}}"}, {"key": "page", "value": "1"}, {"key": "limit", "value": "10"}]}}, "response": []}, {"name": "Get Division by ID", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/divisions/:id", "host": ["{{base_url}}"], "path": ["divisions", ":id"], "variable": [{"key": "id", "value": "division-id-here"}]}}, "response": []}, {"name": "Create Division", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"ministry_id\": \"{{ministry_id}}\",\n  \"department_id\": \"{{department_id}}\",\n  \"name_th\": \"กองบัญชีกลาง\",\n  \"name_en\": \"Central Accounting Division\",\n  \"code\": 3001,\n  \"short_name_th\": \"กบก.\",\n  \"short_name_en\": \"CAD\"\n}"}, "url": {"raw": "{{base_url}}/divisions", "host": ["{{base_url}}"], "path": ["divisions"]}}, "response": []}, {"name": "Update Division", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"ministry_id\": \"{{ministry_id}}\",\n  \"department_id\": \"{{department_id}}\",\n  \"name_th\": \"กองบัญชีกลาง (อัปเดต)\",\n  \"name_en\": \"Central Accounting Division (Updated)\",\n  \"code\": 3001,\n  \"short_name_th\": \"กบก.\",\n  \"short_name_en\": \"CAD\"\n}"}, "url": {"raw": "{{base_url}}/divisions/:id", "host": ["{{base_url}}"], "path": ["divisions", ":id"], "variable": [{"key": "id", "value": "division-id-here"}]}}, "response": []}, {"name": "Delete Division", "request": {"method": "DELETE", "header": [], "url": {"raw": "{{base_url}}/divisions/:id", "host": ["{{base_url}}"], "path": ["divisions", ":id"], "variable": [{"key": "id", "value": "division-id-here"}]}}, "response": []}]}]}