package services

import (
	"testing"

	"gitlab.finema.co/finema/csp/csp-api/models"
	core "gitlab.finema.co/finema/idin-core"
)

func TestDashboardService_GetProviderTypeStats(t *testing.T) {
	// Mock context - you'll need to implement proper mocking based on your test setup
	// This is a basic structure for the test
	
	tests := []struct {
		name     string
		status   []models.ProjectStatus
		wantErr  bool
		expected int // expected number of provider types
	}{
		{
			name:     "Get all provider type stats without status filter",
			status:   []models.ProjectStatus{},
			wantErr:  false,
			expected: 3, // HWC, AWS, CHM
		},
		{
			name:     "Get provider type stats for ACTIVE status",
			status:   []models.ProjectStatus{models.ProjectStatusActive},
			wantErr:  false,
			expected: 3,
		},
		{
			name:     "Get provider type stats for DRAFT status",
			status:   []models.ProjectStatus{models.ProjectStatusDraft},
			wantErr:  false,
			expected: 3,
		},
		{
			name:     "Get provider type stats for CLOSED status",
			status:   []models.ProjectStatus{models.ProjectStatusClosed},
			wantErr:  false,
			expected: 3,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// You'll need to set up a proper mock context here
			// This is just a placeholder structure
			
			// mockCtx := setupMockContext() // Implement this based on your testing framework
			// s := dashboardService{ctx: mockCtx}
			
			// var stats []ProviderTypeStat
			// var err core.IError
			
			// if len(tt.status) > 0 {
			// 	stats, err = s.GetProviderTypeStats(tt.status[0])
			// } else {
			// 	stats, err = s.GetProviderTypeStats()
			// }
			
			// if (err != nil) != tt.wantErr {
			// 	t.Errorf("GetProviderTypeStats() error = %v, wantErr %v", err, tt.wantErr)
			// 	return
			// }
			
			// if len(stats) != tt.expected {
			// 	t.Errorf("GetProviderTypeStats() returned %d stats, expected %d", len(stats), tt.expected)
			// }
			
			// Verify that all provider types are included
			// expectedTypes := []models.ProjectProviderType{
			// 	models.ProjectProviderTypeHWC,
			// 	models.ProjectProviderTypeAWS,
			// 	models.ProjectProviderTypeCHM,
			// }
			
			// for _, expectedType := range expectedTypes {
			// 	found := false
			// 	for _, stat := range stats {
			// 		if stat.ProviderType == expectedType {
			// 			found = true
			// 			break
			// 		}
			// 	}
			// 	if !found {
			// 		t.Errorf("Provider type %s not found in stats", expectedType)
			// 	}
			// }
		})
	}
}

// Example of how you might set up a mock context
// func setupMockContext() core.IContext {
// 	// Implement based on your testing framework and mock setup
// 	// This might involve setting up a test database or mocking the database calls
// 	return nil
// }
