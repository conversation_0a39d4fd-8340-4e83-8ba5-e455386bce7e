package services

import (
	"net/http"

	"gitlab.finema.co/finema/csp/csp-api/helpers"
	"gitlab.finema.co/finema/csp/csp-api/models"
	"gitlab.finema.co/finema/csp/csp-api/repo"
	core "gitlab.finema.co/finema/idin-core"
	"gitlab.finema.co/finema/idin-core/repository"
	"gitlab.finema.co/finema/idin-core/utils"
)

type IMeService interface {
	GetProfile(userID string) (*models.User, core.IError)
	UpdateProfile(userID string, input *MeUpdatePayload) (*models.User, core.IError)
	ChangePassword(userID string, input *ChangePasswordPayload) core.IError
	GetDevices(userID string, pageOptions *core.PageOptions) (*repository.Pagination[models.UserToken], core.IError)
	DeleteDevice(userID, tokenID string) core.IError
}

type meService struct {
	ctx core.IContext
}

type MeUpdatePayload struct {
	DisplayName string
}

type ChangePasswordPayload struct {
	CurrentPassword string
	NewPassword     string
}

func (s meService) GetProfile(userID string) (*models.User, core.IError) {
	user, ierr := repo.User(s.ctx).FindOne("id = ?", userID)
	if ierr != nil {
		return nil, s.ctx.NewError(ierr, ierr)
	}

	return user, nil
}

func (s meService) UpdateProfile(userID string, input *MeUpdatePayload) (*models.User, core.IError) {
	user, ierr := s.GetProfile(userID)
	if ierr != nil {
		return nil, s.ctx.NewError(ierr, ierr)
	}
	if input.DisplayName != "" {
		user.DisplayName = utils.ToPointer(input.DisplayName)
	}

	ierr = repo.User(s.ctx).Where("id = ?", userID).Updates(user)
	if ierr != nil {
		return nil, s.ctx.NewError(ierr, ierr)
	}

	return s.GetProfile(userID)
}

func (s meService) ChangePassword(userID string, input *ChangePasswordPayload) core.IError {
	// Get user to verify current password
	user, ierr := s.GetProfile(userID)
	if ierr != nil {
		return s.ctx.NewError(ierr, ierr)
	}

	// Verify current password
	if !helpers.CheckPasswordHash(input.CurrentPassword, user.Password) {
		return s.ctx.NewError(core.Error{
			Status:  http.StatusBadRequest,
			Code:    "INVALID_CURRENT_PASSWORD",
			Message: "Current password is incorrect",
		}, core.Error{})
	}

	// Hash new password
	hashedPassword, err := helpers.HashPassword(input.NewPassword)
	if err != nil {
		return s.ctx.NewError(core.Error{
			Status:  http.StatusInternalServerError,
			Code:    "PASSWORD_HASH_FAILED",
			Message: "Failed to hash password",
		}, core.Error{})
	}

	// Update password
	user.Password = hashedPassword
	user.IsRequiredResetPassword = false
	user.UpdatedAt = utils.GetCurrentDateTime()

	ierr = repo.User(s.ctx).Where("id = ?", userID).Updates(user)
	if ierr != nil {
		return s.ctx.NewError(ierr, ierr)
	}

	return nil
}

func (s meService) GetDevices(userID string, pageOptions *core.PageOptions) (*repository.Pagination[models.UserToken], core.IError) {
	return repo.UserToken(s.ctx, repo.UserTokenOrderBy(pageOptions)).Where("user_id = ?", userID).Pagination(pageOptions)
}

func (s meService) DeleteDevice(userID, tokenID string) core.IError {
	// Verify the token belongs to the user
	_, ierr := repo.UserToken(s.ctx).FindOne("id = ? AND user_id = ?", tokenID, userID)
	if ierr != nil {
		return s.ctx.NewError(ierr, ierr)
	}

	return repo.UserToken(s.ctx).Delete("id = ? AND user_id = ?", tokenID, userID)
}

func NewMeService(ctx core.IContext) IMeService {
	return &meService{ctx: ctx}
}
