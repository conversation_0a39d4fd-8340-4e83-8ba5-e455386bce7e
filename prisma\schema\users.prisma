model users {
  id                         String        @id @default(uuid()) @db.Uuid
  email                      String        @unique
  password                   String
  display_name               String?
  created_at                 DateTime      @default(now())
  updated_at                 DateTime      @default(now()) @updatedAt
  deleted_at                 DateTime?
  type                       UserType      @default(USER)
  is_required_reset_password Bo<PERSON>an       @default(true)
  created_by_id              String?       @db.Uuid
  deleted_by_id              String?       @db.Uuid
  updated_by_id              String?       @db.Uuid
  createdProjects            projects[]    @relation("ProjectCreator")
  deletedProjects            projects[]    @relation("ProjectDeleter")
  updatedProjects            projects[]    @relation("ProjectUpdater")
  access_tokens              user_tokens[]
  createdBy                  users?        @relation("UserCreatedBy", fields: [created_by_id], references: [id])
  createdUsers               users[]       @relation("UserCreatedBy")
  deletedBy                  users?        @relation("UserDeletedBy", fields: [deleted_by_id], references: [id])
  deletedUsers               users[]       @relation("UserDeletedBy")
  updatedBy                  users?        @relation("UserUpdatedBy", fields: [updated_by_id], references: [id])
  updatedUsers               users[]       @relation("UserUpdatedBy")

  @@index([email])
}

enum UserType {
  USER
  ADMIN
}
