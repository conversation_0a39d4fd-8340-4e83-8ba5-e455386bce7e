model divisions {
  id            String      @id @default(uuid()) @db.Uuid
  ministry_id   String      @db.Uuid
  department_id String      @db.Uuid
  name_th       String      @unique
  name_en       String?     @unique
  short_name_th String?     @unique
  short_name_en String?     @unique
  code          Int?        @unique
  created_at    DateTime    @default(now())
  updated_at    DateTime    @updatedAt
  deleted_at    DateTime?
  department    departments @relation(fields: [department_id], references: [id], onDelete: Cascade)
  ministry      ministries  @relation(fields: [ministry_id], references: [id], onDelete: Cascade)
  projects      projects[]

  @@index([ministry_id])
  @@index([department_id])
  @@index([name_th])
}
