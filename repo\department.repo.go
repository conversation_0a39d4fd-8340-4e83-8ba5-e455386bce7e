package repo

import (
	"gitlab.finema.co/finema/csp/csp-api/models"
	core "gitlab.finema.co/finema/idin-core"
	"gitlab.finema.co/finema/idin-core/repository"
)

type DepartmentOption func(repository.IRepository[models.Department])

var Department = func(c core.IContext, options ...DepartmentOption) repository.IRepository[models.Department] {
	r := repository.New[models.Department](c)
	for _, opt := range options {
		opt(r)
	}

	return r
}

func DepartmentOrderBy(pageOptions *core.PageOptions) DepartmentOption {
	return func(c repository.IRepository[models.Department]) {
		if len(pageOptions.OrderBy) == 0 {
			c.Order("created_at DESC")
		} else {
			c.Order(pageOptions.OrderBy)
		}
	}
}

func DepartmentWithMinistry() DepartmentOption {
	return func(c repository.IRepository[models.Department]) {
		c.Preload("Ministry")
	}
}

func DepartmentWithDivisions() DepartmentOption {
	return func(c repository.IRepository[models.Department]) {
		c.Preload("Divisions")
	}
}

func DepartmentWithProjects() DepartmentOption {
	return func(c repository.IRepository[models.Department]) {
		c.Preload("Projects")
	}
}

func DepartmentByMinistry(ministryID string) DepartmentOption {
	return func(c repository.IRepository[models.Department]) {
		c.Where("ministry_id = ?", ministryID)
	}
}

func DepartmentWithSearch(search string) DepartmentOption {
	if search == "" {
		return func(c repository.IRepository[models.Department]) {}
	}
	return func(c repository.IRepository[models.Department]) {
		searchPattern := "%" + search + "%"
		c.Where("name_th ILIKE ? OR name_en ILIKE ?", searchPattern, searchPattern)
	}
}
