package projectusage

import (
	"net/http"

	"gitlab.finema.co/finema/csp/csp-api/services"
	core "gitlab.finema.co/finema/idin-core"
)

type ProjectUsageController struct{}

func (m ProjectUsageController) GetUsageJob(c core.ICronjobContext) error {
	c.Log().Info("CreateJob AgentBilling is process...")
	agbSvc := services.NewProjectUsageService(c)
	ierr := agbSvc.GetUsage()
	if ierr != nil {
		c.Log().Error(ierr, ierr)
		return nil
	}

	return nil
}

func (m ProjectUsageController) GetUsage(c core.IHTTPContext) error {
	agbSvc := services.NewProjectUsageService(c)
	ierr := agbSvc.GetUsage()
	if ierr != nil {
		return c.JSON(ierr.GetStatus(), ierr.JSON())
	}

	return c.<PERSON>SO<PERSON>(http.StatusOK, "success")
}
