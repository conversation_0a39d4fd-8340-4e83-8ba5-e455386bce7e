package dashboard

import (
	"net/http"

	"gitlab.finema.co/finema/csp/csp-api/services"
	core "gitlab.finema.co/finema/idin-core"
)

type DashboardController struct {
}

func (d DashboardController) GetDashboard(c core.IHTTPContext) error {
	dashboardSvc := services.NewDashboardService(c)
	dashboard, err := dashboardSvc.GetDashboardData()
	if err != nil {
		return c.JSON(err.GetStatus(), err.JSON())
	}

	return c.JSON(http.StatusOK, dashboard)
}
