model user_tokens {
  id          String   @id @default(uuid()) @db.Uuid
  user_id     String   @db.Uuid
  token       String   @unique
  ip_address  String?
  user_agent  String?
  device_info String?
  created_at  DateTime @default(now())
  updated_at  DateTime @default(now()) @updatedAt
  user        users    @relation(fields: [user_id], references: [id], onDelete: Cascade)

  @@index([user_id])
  @@index([token])
}
