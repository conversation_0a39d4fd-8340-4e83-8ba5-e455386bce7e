### Project Usages API Test File
### This file demonstrates the new projects/:id/usages endpoint

@base_url = http://localhost:3001
@auth_token = your_auth_token_here
@project_id = your_project_id_here

### Get Project Usages (Current Cycle Only - Default)
GET {{base_url}}/projects/{{project_id}}/usages
Authorization: Bearer {{auth_token}}

### Get Project Usages (Current Cycle Only - Explicit)
GET {{base_url}}/projects/{{project_id}}/usages?page=1&limit=10
Authorization: Bearer {{auth_token}}

### Get Project Usages (All Cycles)
GET {{base_url}}/projects/{{project_id}}/usages?all=true
Authorization: Bearer {{auth_token}}

### Get Project Usages (All Cycles with Pagination)
GET {{base_url}}/projects/{{project_id}}/usages?all=true&page=1&limit=5
Authorization: Bearer {{auth_token}}

### Get Project Usages (Current Cycle with Search)
GET {{base_url}}/projects/{{project_id}}/usages?q=1000
Authorization: Bearer {{auth_token}}

### Get Project Usages (All Cycles with Search)
GET {{base_url}}/projects/{{project_id}}/usages?all=true&q=1000&page=1&limit=10
Authorization: Bearer {{auth_token}}

### Get Project Usages (Current Cycle with Custom Ordering)
GET {{base_url}}/projects/{{project_id}}/usages?order_by=amount DESC
Authorization: Bearer {{auth_token}}

### Get Project Usages (All Cycles with Custom Ordering)
GET {{base_url}}/projects/{{project_id}}/usages?all=true&order_by=timestamp DESC
Authorization: Bearer {{auth_token}}

### Test with Invalid Project ID (Should return 404)
GET {{base_url}}/projects/invalid-uuid/usages
Authorization: Bearer {{auth_token}}
