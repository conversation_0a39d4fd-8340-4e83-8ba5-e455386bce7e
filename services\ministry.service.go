package services

import (
	"gitlab.finema.co/finema/csp/csp-api/models"
	"gitlab.finema.co/finema/csp/csp-api/repo"
	core "gitlab.finema.co/finema/idin-core"
	"gitlab.finema.co/finema/idin-core/repository"
	"gitlab.finema.co/finema/idin-core/utils"
)

type IMinistryService interface {
	Create(input *MinistryCreatePayload) (*models.Ministry, core.IError)
	Update(id string, input *MinistryUpdatePayload) (*models.Ministry, core.IError)
	Find(id string) (*models.Ministry, core.IError)
	Pagination(pageOptions *core.PageOptions) (*repository.Pagination[models.Ministry], core.IError)
	Delete(id string) core.IError
}

type ministryService struct {
	ctx core.IContext
}

func (s ministryService) Create(input *MinistryCreatePayload) (*models.Ministry, core.IError) {
	ministry := &models.Ministry{
		BaseModel:   models.NewBaseModel(),
		NameTh:      input.NameTh,
		NameEn:      utils.ToPointer(input.NameEn),
		Code:        input.Code,
		ShortNameTh: input.ShortNameTh,
		ShortNameEn: input.ShortNameEn,
	}

	ierr := repo.Ministry(s.ctx).Create(ministry)
	if ierr != nil {
		return nil, s.ctx.NewError(ierr, ierr)
	}

	return s.Find(ministry.ID)
}

func (s ministryService) Update(id string, input *MinistryUpdatePayload) (*models.Ministry, core.IError) {
	ministry, ierr := s.Find(id)
	if ierr != nil {
		return nil, s.ctx.NewError(ierr, ierr)
	}

	// Update fields if provided
	if input.NameTh != "" {
		ministry.NameTh = input.NameTh
	}
	if input.NameEn != "" {
		ministry.NameEn = utils.ToPointer(input.NameEn)
	}
	if input.Code != nil {
		ministry.Code = input.Code
	}
	if input.ShortNameTh != nil {
		ministry.ShortNameTh = input.ShortNameTh
	}
	if input.ShortNameEn != nil {
		ministry.ShortNameEn = input.ShortNameEn
	}

	// Update timestamp
	ministry.UpdatedAt = utils.GetCurrentDateTime()

	ierr = repo.Ministry(s.ctx).Where("id = ?", id).Updates(ministry)
	if ierr != nil {
		return nil, s.ctx.NewError(ierr, ierr)
	}

	return s.Find(ministry.ID)
}

func (s ministryService) Find(id string) (*models.Ministry, core.IError) {
	return repo.Ministry(s.ctx).FindOne("id = ?", id)
}

func (s ministryService) Pagination(pageOptions *core.PageOptions) (*repository.Pagination[models.Ministry], core.IError) {
	return repo.Ministry(s.ctx, repo.MinistryOrderBy(pageOptions), repo.MinistryWithSearch(pageOptions.Q)).Pagination(pageOptions)
}

func (s ministryService) Delete(id string) core.IError {
	_, ierr := s.Find(id)
	if ierr != nil {
		return s.ctx.NewError(ierr, ierr)
	}

	return repo.Ministry(s.ctx).Delete("id = ?", id)
}

func NewMinistryService(ctx core.IContext) IMinistryService {
	return &ministryService{ctx: ctx}
}
