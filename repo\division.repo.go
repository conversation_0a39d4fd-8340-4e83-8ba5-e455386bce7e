package repo

import (
	"gitlab.finema.co/finema/csp/csp-api/models"
	core "gitlab.finema.co/finema/idin-core"
	"gitlab.finema.co/finema/idin-core/repository"
)

type DivisionOption func(repository.IRepository[models.Division])

var Division = func(c core.IContext, options ...DivisionOption) repository.IRepository[models.Division] {
	r := repository.New[models.Division](c)
	for _, opt := range options {
		opt(r)
	}

	return r
}

func DivisionOrderBy(pageOptions *core.PageOptions) DivisionOption {
	return func(c repository.IRepository[models.Division]) {
		if len(pageOptions.OrderBy) == 0 {
			c.Order("created_at DESC")
		} else {
			c.Order(pageOptions.OrderBy)
		}
	}
}

func DivisionWithMinistry() DivisionOption {
	return func(c repository.IRepository[models.Division]) {
		c.Preload("Ministry")
	}
}

func DivisionWithDepartment() DivisionOption {
	return func(c repository.IRepository[models.Division]) {
		c.Preload("Department")
	}
}

func DivisionWithProjects() DivisionOption {
	return func(c repository.IRepository[models.Division]) {
		c.Preload("Projects")
	}
}

func DivisionByMinistry(ministryID string) DivisionOption {
	return func(c repository.IRepository[models.Division]) {
		c.Where("ministry_id = ?", ministryID)
	}
}

func DivisionByDepartment(departmentID string) DivisionOption {
	return func(c repository.IRepository[models.Division]) {
		c.Where("department_id = ?", departmentID)
	}
}

func DivisionWithSearch(search string) DivisionOption {
	if search == "" {
		return func(c repository.IRepository[models.Division]) {}
	}
	return func(c repository.IRepository[models.Division]) {
		searchPattern := "%" + search + "%"
		c.Where("name_th ILIKE ? OR name_en ILIKE ?", searchPattern, searchPattern)
	}
}
