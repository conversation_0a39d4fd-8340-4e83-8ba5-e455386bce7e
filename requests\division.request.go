package requests

import (
	"gitlab.finema.co/finema/csp/csp-api/models"
	"gitlab.finema.co/finema/csp/csp-api/repo"
	core "gitlab.finema.co/finema/idin-core"
	"gitlab.finema.co/finema/idin-core/utils"
)

type DivisionCreate struct {
	core.BaseValidator
	MinistryID   *string `json:"ministry_id"`
	DepartmentID *string `json:"department_id"`
	NameTh       *string `json:"name_th"`
	NameEn       *string `json:"name_en"`
}

func (r *DivisionCreate) Valid(ctx core.IContext) core.IError {
	r.Must(r.<PERSON>tr<PERSON>equired(r.MinistryID, "ministry_id"))
	r.Must(r.<PERSON>tr<PERSON>equired(r.DepartmentID, "department_id"))
	r.Must(r.IsStrRequired(r.NameTh, "name_th"))
	r.Must(r.IsStrRequired(r.NameEn, "name_en"))

	r.Must(r.Is<PERSON>trUnique(ctx, r.NameTh, models.Department{}.TableName(), "name_th", "", "name_th"))
	r.Must(r.Is<PERSON>trUnique(ctx, r.NameEn, models.Department{}.TableName(), "name_en", "", "name_en"))

	// Check if ministry exists
	if r.MinistryID != nil {
		r.Must(r.IsExists(ctx, r.MinistryID, models.Ministry{}.TableName(), "id", "ministry_id"))
	}

	// Check if department exists
	if r.DepartmentID != nil {
		r.Must(r.IsExists(ctx, r.DepartmentID, models.Department{}.TableName(), "id", "department_id"))
	}
	return r.Error()
}

type DivisionUpdate struct {
	core.BaseValidator
	MinistryID   *string `json:"ministry_id"`
	DepartmentID *string `json:"department_id"`
	NameTh       *string `json:"name_th"`
	NameEn       *string `json:"name_en"`
}

func (r *DivisionUpdate) Valid(ctx core.IContext) core.IError {
	cc := ctx.(core.IHTTPContext)
	oldNameTh := ""
	oldNameEn := ""
	division, _ := repo.Division(cc).FindOne("id = ?", cc.Param("id"))
	if division != nil {
		oldNameTh = division.NameTh
		if division.NameEn != nil {
			oldNameEn = utils.ToNonPointer(division.NameEn)
		}
	}

	if utils.ToNonPointer(r.NameTh) != "" {
		r.Must(r.IsStrUnique(ctx, r.NameTh, models.Division{}.TableName(), "name_th", oldNameTh, "name_th"))
	}

	if utils.ToNonPointer(r.NameEn) != "" {
		r.Must(r.IsStrUnique(ctx, r.NameEn, models.Division{}.TableName(), "name_en", oldNameEn, "name_en"))
	}

	// Check if ministry exists if provided
	if r.MinistryID != nil {
		r.Must(r.IsExists(ctx, r.MinistryID, models.Ministry{}.TableName(), "id", "ministry_id"))
	}

	// Check if department exists if provided
	if r.DepartmentID != nil {
		r.Must(r.IsExists(ctx, r.DepartmentID, models.Department{}.TableName(), "id", "department_id"))
	}

	return r.Error()
}
