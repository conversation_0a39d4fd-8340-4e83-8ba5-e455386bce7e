package requests

import (
	"gitlab.finema.co/finema/csp/csp-api/models"
	"gitlab.finema.co/finema/csp/csp-api/repo"
	core "gitlab.finema.co/finema/idin-core"
	"gitlab.finema.co/finema/idin-core/utils"
)

type MinistryCreate struct {
	core.BaseValidator
	NameTh *string `json:"name_th"`
	NameEn *string `json:"name_en"`
}

func (r *MinistryCreate) Valid(ctx core.IContext) core.IError {
	r.Must(r.IsStrRequired(r.NameTh, "name_th"))
	r.Must(r.<PERSON>tr<PERSON>equired(r.NameEn, "name_en"))
	r.Must(r.<PERSON>(ctx, r.NameTh, models.Department{}.TableName(), "name_th", "", "name_th"))
	r.Must(r.IsStrUnique(ctx, r.NameEn, models.Department{}.TableName(), "name_en", "", "name_en"))

	return r.<PERSON>rror()
}

type MinistryUpdate struct {
	core.BaseValidator
	NameTh *string `json:"name_th"`
	NameEn *string `json:"name_en"`
}

func (r *MinistryUpdate) Valid(ctx core.IContext) core.IError {
	cc := ctx.(core.IHTTPContext)
	oldNameTh := ""
	oldNameEn := ""

	ministry, _ := repo.Ministry(cc).FindOne("id = ?", cc.Param("id"))
	if ministry != nil {
		oldNameTh = ministry.NameTh
		if ministry.NameEn != nil {
			oldNameEn = utils.ToNonPointer(ministry.NameEn)
		}
	}

	if utils.ToNonPointer(r.NameTh) != "" {
		r.Must(r.IsStrUnique(ctx, r.NameTh, models.Ministry{}.TableName(), "name_th", oldNameTh, "name_th"))
	}

	if utils.ToNonPointer(r.NameEn) != "" {
		r.Must(r.IsStrUnique(ctx, r.NameEn, models.Ministry{}.TableName(), "name_en", oldNameEn, "name_en"))
	}

	return r.Error()
}
