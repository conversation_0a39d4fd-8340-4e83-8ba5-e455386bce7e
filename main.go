package main

import (
	"log"

	"gitlab.finema.co/finema/csp/csp-api/cmd"
	"gitlab.finema.co/finema/csp/csp-api/consts"
	core "gitlab.finema.co/finema/idin-core"
)

func main() {
	// cmd.APIRun()

	switch core.NewEnv().Config().Service {
	case consts.EnvServiceAPI:
		cmd.APIRun()
	case consts.EnvServiceCronjob:
		cmd.CronjobRun()
	case consts.EnvServiceCronjobProjectUsage:
		cmd.CronjobProjectUsageRun()
	default:
		log.Fatal("Service not support")
	}
}
